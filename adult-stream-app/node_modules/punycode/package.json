{"name": "punycode", "version": "2.3.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/mathiasbynens/punycode.js.git"}, "bugs": "https://github.com/mathiasbynens/punycode.js/issues", "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "scripts": {"test": "mocha tests", "build": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "mocha": "^10.2.0"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}}