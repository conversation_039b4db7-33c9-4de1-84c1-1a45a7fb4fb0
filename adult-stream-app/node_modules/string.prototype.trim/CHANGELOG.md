# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.10](https://github.com/es-shims/String.prototype.trim/compare/v1.2.9...v1.2.10) - 2024-12-11

### Commits

- [actions] split out node 10-20, and 20+ [`335d99a`](https://github.com/es-shims/String.prototype.trim/commit/335d99a408ee623eb60ca53af13da1813d3ee1b8)
- [Refactor] use `define-data-property` and `has-property-descriptors` directly [`2e0c2e9`](https://github.com/es-shims/String.prototype.trim/commit/2e0c2e9979c9368b13a90a31ae79ca8b1f2fa01a)
- [<PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`138d3db`](https://github.com/es-shims/String.prototype.trim/commit/138d3db824e6fed5a0f9c81ae06534ef4e20b969)
- [Deps] update `call-bind`, `es-abstract` [`3a06731`](https://github.com/es-shims/String.prototype.trim/commit/3a0673152cc347f42feed04a6936d824ffb2bb57)
- [Refactor] use `call-bound` directly [`9499206`](https://github.com/es-shims/String.prototype.trim/commit/9499206837e054fac9b48db0f63c09dd1e955e11)
- [Tests] replace `aud` with `npm audit` [`c88a935`](https://github.com/es-shims/String.prototype.trim/commit/c88a935c098d8a9809812af68c1fc51ab65854cb)
- [Dev Deps] add missing dev dep [`9667c7d`](https://github.com/es-shims/String.prototype.trim/commit/9667c7d04de2dff034cde13f41aca9e203947d43)
- [Dev Deps] add missing peer dep [`6417c72`](https://github.com/es-shims/String.prototype.trim/commit/6417c727c94acb568ad4f5b4674996401f5ee531)

## [v1.2.9](https://github.com/es-shims/String.prototype.trim/compare/v1.2.8...v1.2.9) - 2024-03-16

### Commits

- [Refactor] use `es-object-atoms`; update `call-bind`, `define-properties`, `es-abstract` [`f6fe1af`](https://github.com/es-shims/String.prototype.trim/commit/f6fe1af4cb9381757971b294afc845e3b2e7c1e9)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`d4e2b81`](https://github.com/es-shims/String.prototype.trim/commit/d4e2b81878478d7090826b73d52bc42f117a2189)

## [v1.2.8](https://github.com/es-shims/String.prototype.trim/compare/v1.2.7...v1.2.8) - 2023-09-07

### Commits

- [Tests] add passing test cases [`2ab172c`](https://github.com/es-shims/String.prototype.trim/commit/2ab172c3ddeec62fb4f6ead3c7e10d24e340ecad)
- [Deps] update `es-abstract` [`8c16598`](https://github.com/es-shims/String.prototype.trim/commit/8c16598aba57e30c5e8446f91fb998a5790c1f81)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`2b99fad`](https://github.com/es-shims/String.prototype.trim/commit/2b99fad6f32386b5bf1b304acb0fdd2a73c77a95)
- [Dev Deps] update `@ljharb/eslint-config`, `@ljharb/eslint-config`, `aud`, `tape` [`97be2b5`](https://github.com/es-shims/String.prototype.trim/commit/97be2b55a3902753f50fadc2e9df6b7ff0f0f669)
- [Deps] update `define-properties`, `es-abstract` [`1fdc65f`](https://github.com/es-shims/String.prototype.trim/commit/1fdc65ff4b9c73cc9d132832943a89a57f1f93a5)

## [v1.2.7](https://github.com/es-shims/String.prototype.trim/compare/v1.2.6...v1.2.7) - 2022-11-07

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`3e6de84`](https://github.com/es-shims/String.prototype.trim/commit/3e6de84f959c1ecc44e894aa1a64a8eb5007f3f5)
- [actions] update rebase action to use reusable workflow [`b725a04`](https://github.com/es-shims/String.prototype.trim/commit/b725a0499b832e69dfe5121bb643464a659ba8ff)
- [Deps] update `es-abstract` [`b707a17`](https://github.com/es-shims/String.prototype.trim/commit/b707a1747363f361665c50eda5eb952a86755e8c)
- [Dev Deps] update `aud`, `tape` [`5295419`](https://github.com/es-shims/String.prototype.trim/commit/5295419b0bbaf8035696ef3a24a8625ee743ec7f)

## [v1.2.6](https://github.com/es-shims/String.prototype.trim/compare/v1.2.5...v1.2.6) - 2022-04-24

### Commits

- [actions] reuse common workflows [`dbfc093`](https://github.com/es-shims/String.prototype.trim/commit/dbfc0932e7b2fad166245599310a1d84a1be0882)
- [Fix] as of unicode v6, the mongolian vowel separator is no longer whitespace [`56bbb86`](https://github.com/es-shims/String.prototype.trim/commit/56bbb86b8e514a16fc47e095b8c1014e12a2e92a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`7fa437e`](https://github.com/es-shims/String.prototype.trim/commit/7fa437e4c3d25798d979294a121f93cb4346ba04)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `functions-have-names`, `tape` [`716a060`](https://github.com/es-shims/String.prototype.trim/commit/716a06080dbca68ec8e3b2e0e18c0a333ed28594)
- [actions] update codecov uploader [`9a39958`](https://github.com/es-shims/String.prototype.trim/commit/9a39958a13e20d6b9f01f9fce71dd37cc03da52e)
- [Fix] ensure main entry point properly checks the receiver in ES3 engines [`24220c4`](https://github.com/es-shims/String.prototype.trim/commit/24220c49dcb7055b5b94eb52c4d610428e19d3e4)
- [Deps] update `define-properties`, `es-abstract` [`c6008ea`](https://github.com/es-shims/String.prototype.trim/commit/c6008ea9007741755710b70ae05003cb71a7d1bd)

## [v1.2.5](https://github.com/es-shims/String.prototype.trim/compare/v1.2.4...v1.2.5) - 2021-10-03

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`37d5a61`](https://github.com/es-shims/String.prototype.trim/commit/37d5a61c95a26c079b30e2d9d2ffdb70f36ca9cf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `tape` [`4c4a85e`](https://github.com/es-shims/String.prototype.trim/commit/4c4a85e5f521ae9811ac1205af933e3845b5a24e)
- [readme] add github actions/codecov badges [`9980eee`](https://github.com/es-shims/String.prototype.trim/commit/9980eee58866b08218ff3257a3af2aa9e29774fc)
- [Deps] update `es-abstract` [`6c1da80`](https://github.com/es-shims/String.prototype.trim/commit/6c1da806270fd909a76fc43e3dfe91be46058382)
- [readme] remove defunct testling badge [`8d282d1`](https://github.com/es-shims/String.prototype.trim/commit/8d282d1becd5f8d3ffe0e6e1dbfa2ddf29071670)
- [Dev Deps] update `eslint`, `tape` [`8856c26`](https://github.com/es-shims/String.prototype.trim/commit/8856c2663b5495cafb930e009f68e2d22958cac0)
- [actions] update workflows [`62cd341`](https://github.com/es-shims/String.prototype.trim/commit/62cd3412aaeee13c0d2cfe7f9bd33297e7279989)
- [meta] use `prepublishOnly` script for npm 7+ [`abd99c4`](https://github.com/es-shims/String.prototype.trim/commit/abd99c4a9838847efb3db7ddf579fb4bda8f6030)
- [Deps] update `es-abstract` [`802cb7b`](https://github.com/es-shims/String.prototype.trim/commit/802cb7b69b3a0c9a9b9642d641f0af490bd9dde7)

## [v1.2.4](https://github.com/es-shims/String.prototype.trim/compare/v1.2.3...v1.2.4) - 2021-02-21

### Commits

- [meta] do not publish github action workflow files [`936161b`](https://github.com/es-shims/String.prototype.trim/commit/936161bf43c83e09bc39c4d472d313c8f64e3fe3)
- [readme] remove travis badge [`9a28c39`](https://github.com/es-shims/String.prototype.trim/commit/9a28c3943b51a2cc87694c954205249122256d92)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `functions-have-names`, `has-strict-mode`, `tape` [`7b4be8d`](https://github.com/es-shims/String.prototype.trim/commit/7b4be8d12365feb5b42658902cf18316508a0c62)
- [Tests] increase coverage [`31b8735`](https://github.com/es-shims/String.prototype.trim/commit/31b87354f55f24501ca8b2f02477cb692a70457c)
- [actions] update workflows [`eda6ab7`](https://github.com/es-shims/String.prototype.trim/commit/eda6ab73fea52b49b74653e96ac81d5372599dc8)
- [Deps] update `call-bind`, `es-abstract` [`083f88f`](https://github.com/es-shims/String.prototype.trim/commit/083f88f5342144f337f1f82874cb8cd6f27f2262)

## [v1.2.3](https://github.com/es-shims/String.prototype.trim/compare/v1.2.2...v1.2.3) - 2020-11-21

### Commits

- [Tests] migrate tests to Github Actions [`6768c8d`](https://github.com/es-shims/String.prototype.trim/commit/6768c8d5569a6fcc841436b98a19ddf6b63c7de4)
- [Tests] run `nyc` on all tests [`2fd5baa`](https://github.com/es-shims/String.prototype.trim/commit/2fd5baa7239a4d449338e0333e2dc4573597962e)
- [Deps] update `es-abstract`; use `call-bind` where applicable [`e4e8c6e`](https://github.com/es-shims/String.prototype.trim/commit/e4e8c6ede423ee8c384564092f624f980097bb28)
- [Dev Deps] update `eslint`, `aud`, `auto-changelog` [`a21c1d5`](https://github.com/es-shims/String.prototype.trim/commit/a21c1d5c87ec9b1b5107b0a1faf138993951081c)

## [v1.2.2](https://github.com/es-shims/String.prototype.trim/compare/v1.2.1...v1.2.2) - 2020-09-15

### Commits

- [Tests] use `nyc` for coverage` [`0884270`](https://github.com/es-shims/String.prototype.trim/commit/0884270b26f7e6f7602d9f355dc3b4d5cd12d97e)
- [Tests] add implementation tests [`475c480`](https://github.com/es-shims/String.prototype.trim/commit/475c4804224a11bb6fba6d1407b59f7aab2c5d4e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`d70d913`](https://github.com/es-shims/String.prototype.trim/commit/d70d9136bc76499ecee1997068b7f3d1ba0f94e8)
- [actions] add "Allow Edits" workflow [`6e6be23`](https://github.com/es-shims/String.prototype.trim/commit/6e6be23aa679e02836e1f3e65bd09dd30c3fd2ae)
- [Refactor] use `RequireObjectCoercible` instead of `CheckObjectCoercible` [`5bfaf17`](https://github.com/es-shims/String.prototype.trim/commit/5bfaf1731edd5fcd01ec07f75717eebdf4992e4c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `functions-have-names`; add `safe-publish-latest` [`65be600`](https://github.com/es-shims/String.prototype.trim/commit/65be600028222127c1faaeacea810bc042f35f70)
- [Deps] update `es-abstract`, remove `function-bind` [`5f4d1ec`](https://github.com/es-shims/String.prototype.trim/commit/5f4d1ec8f2c13a4b4fde171ca2029e1ec4057b2b)
- [Refactor] switch from 2019 to 2020 AOs [`4c2d5d2`](https://github.com/es-shims/String.prototype.trim/commit/4c2d5d23f63731958007d856767f5a2f9c779c15)
- [Dev Deps] update `auto-changelog`, `tape` [`c7fc9e2`](https://github.com/es-shims/String.prototype.trim/commit/c7fc9e22b09d24a9aa9e68358d4a5ba10e25ff04)
- [Dev Deps] update `auto-changelog`; add `aud` [`e1dec36`](https://github.com/es-shims/String.prototype.trim/commit/e1dec364391ea5d7dc8339d2da279fdc511cdbe9)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`35826c2`](https://github.com/es-shims/String.prototype.trim/commit/35826c21b140a3c1431bd20c3443da23888b92bc)
- [Deps] update `es-abstract` [`54095ef`](https://github.com/es-shims/String.prototype.trim/commit/54095ef76d193c8595516977e98343f8db754e2d)
- [Deps] update `es-abstract` [`486dd9c`](https://github.com/es-shims/String.prototype.trim/commit/486dd9c68c5843a9f50cde55749d34edb9702616)

## [v1.2.1](https://github.com/es-shims/String.prototype.trim/compare/v1.2.0...v1.2.1) - 2019-12-16

### Commits

- [Tests] use shared travis-ci configs [`52f7e64`](https://github.com/es-shims/String.prototype.trim/commit/52f7e643f128e04ed0139c1cb3da7b7907ea639f)
- [meta] add `auto-changelog` [`6284c06`](https://github.com/es-shims/String.prototype.trim/commit/6284c061c8f4de4314a43e8600ff879164ab162c)
- [meta] remove unused Makefile and associated utilities [`8c781cd`](https://github.com/es-shims/String.prototype.trim/commit/8c781cd4bad87f7bf31dae256ef0f1c19cec2113)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names` [`c54b481`](https://github.com/es-shims/String.prototype.trim/commit/c54b481743611e736315c10885cfc861d306e64a)
- [Refactor] use split-up `es-abstract` (57% bundle size decrease) [`b0378c9`](https://github.com/es-shims/String.prototype.trim/commit/b0378c99988846b0a8edf3fcfd08f30dd632f5a9)
- [actions] add automatic rebasing / merge commit blocking [`bffe893`](https://github.com/es-shims/String.prototype.trim/commit/bffe893990837250f8a3c4714bd52675251af5c7)
- [meta] add `funding` field [`0559449`](https://github.com/es-shims/String.prototype.trim/commit/05594490366fad2d8e4bd435937fbd81169d965a)
- [Deps] update `es-abstract` [`c44d307`](https://github.com/es-shims/String.prototype.trim/commit/c44d307a50bb1fc53811b49d9203cd794ed1bae4)

## [v1.2.0](https://github.com/es-shims/String.prototype.trim/compare/v1.1.2...v1.2.0) - 2019-07-24

### Commits

- [Tests] up to `node` `v12.6`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v7.10`, `v6.17`, `4.9`; use `nvm install-latest-npm` [`b857148`](https://github.com/es-shims/String.prototype.trim/commit/b857148644d305f720b2dd9bf691b7dc2930f91d)
- [Tests] remove `jscs` [`ad1dea7`](https://github.com/es-shims/String.prototype.trim/commit/ad1dea7f6114de79d637e82cdfaf1aa5499bd358)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `replace`, `semver`, `tape` [`fcbc11d`](https://github.com/es-shims/String.prototype.trim/commit/fcbc11d4af9be65f4f7eb4aec20498c7a33a004f)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`06a4ffa`](https://github.com/es-shims/String.prototype.trim/commit/06a4ffa3078d4ab65418e878d3ef7b03277e8a1f)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@es-shims/api` [`3554fb1`](https://github.com/es-shims/String.prototype.trim/commit/3554fb1fe722004080000b0767f7f89676a3d73a)
- [Dev Deps] update `nsp`, `eslint`, `@ljharb/eslint-config` [`804b2f2`](https://github.com/es-shims/String.prototype.trim/commit/804b2f244c257b32cb2473eb1a829ce97dc6a0a5)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `semver`, `@ljharb/eslint-config` [`6a69408`](https://github.com/es-shims/String.prototype.trim/commit/6a694081cdd9aa9296ea181e4d2b4c6fe656bb16)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`e89adee`](https://github.com/es-shims/String.prototype.trim/commit/e89adeefcbd6fb59563e62d230b8036d65a8bf69)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`1280e56`](https://github.com/es-shims/String.prototype.trim/commit/1280e5629deb4554c7077d0e452399c03c06f7b1)
- [New] add `auto` entry point [`bb00b15`](https://github.com/es-shims/String.prototype.trim/commit/bb00b1551d2774e216b5d316b552d08a7f0619d4)
- [Tests] fix tests for the mongolian vowel separator [`a35f627`](https://github.com/es-shims/String.prototype.trim/commit/a35f6275ec7b4d6b47136a007fd07566e037cac8)
- [Tests] up to `node` `v5.9`, `v4.4` [`b541b9b`](https://github.com/es-shims/String.prototype.trim/commit/b541b9b46873be859aea6c5b1e7f6f54323ea139)
- [Dev Deps] update `jscs`, `nsp`, `eslint` [`b52022d`](https://github.com/es-shims/String.prototype.trim/commit/b52022d809922914827c34bcf2c1f81b68bde092)
- [Tests] use pretest/posttest for linting/security [`39f5684`](https://github.com/es-shims/String.prototype.trim/commit/39f56844f30f630ab5497f38153b8f0646ed1d96)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`8c358c2`](https://github.com/es-shims/String.prototype.trim/commit/8c358c22ee57bd5e7b437fb707230399ea7c42aa)
- [Tests] up to `node` `v6.2` [`2ac7e1f`](https://github.com/es-shims/String.prototype.trim/commit/2ac7e1f90088bb0c96986006539ffebb6b2a6eda)
- Only apps should have lockfiles [`cb15ed5`](https://github.com/es-shims/String.prototype.trim/commit/cb15ed57fbf3ba3f2d7f24af957e8ff41421dd81)
- [Deps] update `define-properties`, `es-abstract`, `function-bind` [`5e0371a`](https://github.com/es-shims/String.prototype.trim/commit/5e0371af5c7b8fee49a0e7d1bcc26fcf41743779)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api` [`37bae7f`](https://github.com/es-shims/String.prototype.trim/commit/37bae7f8f4952b376036d924673b9e885da022f8)
- [Tests] on `node` `v5.6`, `v4.3` [`33017cf`](https://github.com/es-shims/String.prototype.trim/commit/33017cf0d3aade480c357ee8aedaca7bc5a75092)
- [Tests] allow coverage to fail [`0d7b1e3`](https://github.com/es-shims/String.prototype.trim/commit/0d7b1e365484f8c33247b60e2a07748dc2183d40)
- [Tests] use `functions-have-names` [`3e68777`](https://github.com/es-shims/String.prototype.trim/commit/3e687776f6a45e3a6d129705aad3bb4863758114)
- [Tests] on `node` `v5.12` [`32ea49d`](https://github.com/es-shims/String.prototype.trim/commit/32ea49d7572b3f981d036ea060e3ed3559ad78ef)
- [Deps] update `es-abstract` [`15f7f24`](https://github.com/es-shims/String.prototype.trim/commit/15f7f249619b7aab941c6c8156cb81ed57c39b7e)
- [Tests] on `node` `v5.10` [`080c50f`](https://github.com/es-shims/String.prototype.trim/commit/080c50fc617de7ac024d443d4a9b76ba295bd744)
- [Deps] update `function-bind` [`532480e`](https://github.com/es-shims/String.prototype.trim/commit/532480e07ad7bf22da64d401ede35928d21ff558)

## [v1.1.2](https://github.com/es-shims/String.prototype.trim/compare/v1.1.1...v1.1.2) - 2016-02-06

### Commits

- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `semver`, `@ljharb/eslint-config` [`df94d07`](https://github.com/es-shims/String.prototype.trim/commit/df94d07e12ca1e52739353f534a3d89e0a860a70)
- [Dev Deps] update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`ef78d89`](https://github.com/es-shims/String.prototype.trim/commit/ef78d89148efe8d371fb828923dd149163e2c5c6)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`b746516`](https://github.com/es-shims/String.prototype.trim/commit/b7465166a48828367bedaa2d42a30a5e148dcaae)
- package.json: use object form of "authors", add "contributors" [`a799df1`](https://github.com/es-shims/String.prototype.trim/commit/a799df17322ae526d5c8732c75bd5bcc3d1f649f)
- [Tests] up to `node` `v5.5`, don’t allow `0.8` to fail [`7fea308`](https://github.com/es-shims/String.prototype.trim/commit/7fea3082424fbeb25f2cb88884a999326ca428f2)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `semver`, `@ljharb/eslint-config` [`d14c7c1`](https://github.com/es-shims/String.prototype.trim/commit/d14c7c1850fb8e3a9f1d153bd818ed3beacdbadc)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`2903359`](https://github.com/es-shims/String.prototype.trim/commit/29033591ca3e65977e2746537c94b7e2e8b65ecc)
- [Tests] fix npm upgrades for older nodes [`0a6cbfa`](https://github.com/es-shims/String.prototype.trim/commit/0a6cbfa0ac506703ed554f6dc8bbde7ac1977cd4)
- [Deps] update `define-properties`, `es-abstract` [`39ccb08`](https://github.com/es-shims/String.prototype.trim/commit/39ccb0881762d762934ef2cb3c0459c65b3abf72)
- [Deps] update `es-abstract` [`c40e4fb`](https://github.com/es-shims/String.prototype.trim/commit/c40e4fb229e1a6a3e3c6818fc680ca380f2d866c)
- Use the polyfill, not the implementation, as the default export. [`0fe847e`](https://github.com/es-shims/String.prototype.trim/commit/0fe847e901871f1a8f33a66be2cd518bf8dcd0e1)
- [Tests] on `node` `v4.2` [`589743c`](https://github.com/es-shims/String.prototype.trim/commit/589743c0f8b1432a79cfed4b29187e6a9760a87b)
- [Deps] update `es-abstract` [`85bad8e`](https://github.com/es-shims/String.prototype.trim/commit/85bad8e217969a6e1eb7679a1bb06d6f075bd557)
- added assert [`aa81ac5`](https://github.com/es-shims/String.prototype.trim/commit/aa81ac55540a9e53a58ce0d1f5266ff36d403b3a)

## [v1.1.1](https://github.com/es-shims/String.prototype.trim/compare/v1.1.0...v1.1.1) - 2015-08-16

### Commits

- [Docs] remove "if" around `.shim` call in example [`b9ce088`](https://github.com/es-shims/String.prototype.trim/commit/b9ce08875f7252a85928e426db9b533cc5ee007a)

## [v1.1.0](https://github.com/es-shims/String.prototype.trim/compare/v1.0.0...v1.1.0) - 2015-08-16

### Commits

- Implement the [es-shim API](es-shims/api). [`5812703`](https://github.com/es-shims/String.prototype.trim/commit/581270337926462f9babf83772bcab71bc48bf8f)
- Move implementation to `implementation.js` [`e455b2a`](https://github.com/es-shims/String.prototype.trim/commit/e455b2af0e360358f02e54b556e698a971fc31f4)
- Fix `make release` [`efd2071`](https://github.com/es-shims/String.prototype.trim/commit/efd20711e9a226ad25e2b1e5ad7bbe85ef832d69)
- [Dev Deps] update `jscs` [`6c2fa95`](https://github.com/es-shims/String.prototype.trim/commit/6c2fa957538f139767de540693910cf1f49c8d3a)
- [Deps] update `es-abstract` [`de4cd87`](https://github.com/es-shims/String.prototype.trim/commit/de4cd876239d550f3c35e1db55a973cbb03c9b77)
- [Dev Deps] update `tape` [`2d07fe1`](https://github.com/es-shims/String.prototype.trim/commit/2d07fe19ff136467e1edbca91cfea6caac1b06d8)
- [Dev Deps] update `tape` [`e697efe`](https://github.com/es-shims/String.prototype.trim/commit/e697efe943cab07639d62649424c72e4f44f2469)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`6065103`](https://github.com/es-shims/String.prototype.trim/commit/6065103baa10f56b904f44693d5ba6b23ee6ba57)

## v1.0.0 - 2015-08-08

### Commits

- Dotfiles / Makefile [`b7f0e52`](https://github.com/es-shims/String.prototype.trim/commit/b7f0e526ea73136e00595d768faaba83a41da7ee)
- Tests [`4d61441`](https://github.com/es-shims/String.prototype.trim/commit/4d61441de9dcf2278dcd118077d4541843a47534)
- package.json [`2a2e0f2`](https://github.com/es-shims/String.prototype.trim/commit/2a2e0f2949dd30095eda20e1796483ea21dd7b38)
- Initial commit [`51aa18f`](https://github.com/es-shims/String.prototype.trim/commit/51aa18f299f8d340034670af0311c5981c89f713)
- Read me [`5681192`](https://github.com/es-shims/String.prototype.trim/commit/56811925aed1dd06a2cfa654b3f246b6a897a33c)
- Implementation [`87f08c5`](https://github.com/es-shims/String.prototype.trim/commit/87f08c565f7be56c2826386aa4a4e683a00d8871)
